import { 
  Injectable, 
  NotFoundException, 
  ForbiddenException, 
  BadRequestException,
  Logger 
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Between, In } from 'typeorm';
import { ManagerDashboardMetrics, DashboardMetricsStatus } from './entities/manager-dashboard-metrics.entity';
import { DashboardReminderSettings } from './entities/dashboard-reminder-settings.entity';
import { OrganizationalUnit } from '../../teams/entities/organizational-unit.entity';
import { User, UserRole } from '../../users/entities/user.entity';
import { AuditLoggingService } from '../../audit/audit-logging.service';
import { 
  CreateManagerDashboardMetricsDto, 
  UpdateManagerDashboardMetricsDto,
  DashboardQueryDto,
  UpdateReminderSettingsDto
} from './dto';

@Injectable()
export class ManagerDashboardService {
  private readonly logger = new Logger(ManagerDashboardService.name);

  constructor(
    @InjectRepository(ManagerDashboardMetrics)
    private readonly metricsRepository: Repository<ManagerDashboardMetrics>,
    @InjectRepository(DashboardReminderSettings)
    private readonly reminderSettingsRepository: Repository<DashboardReminderSettings>,
    @InjectRepository(OrganizationalUnit)
    private readonly orgUnitRepository: Repository<OrganizationalUnit>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    private readonly auditLoggingService: AuditLoggingService,
  ) {}

  /**
   * Get dashboard metrics for a manager with access control
   */
  async getDashboardMetricsForManager(
    query: DashboardQueryDto,
    requestingUserId: number,
  ): Promise<{ metrics: ManagerDashboardMetrics[]; total: number }> {
    const startTime = Date.now();
    
    try {
      // Validate access permissions
      if (query.managerId) {
        await this.validateManagerAccess(query.managerId, requestingUserId);
      }

      const queryBuilder = this.metricsRepository.createQueryBuilder('metrics')
        .leftJoinAndSelect('metrics.organizationalUnit', 'orgUnit')
        .leftJoinAndSelect('orgUnit.parent', 'parentUnit')
        .leftJoinAndSelect('metrics.manager', 'manager')
        .leftJoinAndSelect('metrics.lastUpdatedByUser', 'updatedBy');

      // Apply filters
      if (query.managerId) {
        queryBuilder.andWhere('metrics.managerId = :managerId', { managerId: query.managerId });
      }

      if (query.reportingPeriod) {
        queryBuilder.andWhere('metrics.reportingPeriod = :reportingPeriod', { 
          reportingPeriod: query.reportingPeriod 
        });
      }

      if (query.organizationalUnitId) {
        queryBuilder.andWhere('metrics.organizationalUnitId = :orgUnitId', { 
          orgUnitId: query.organizationalUnitId 
        });
      }

      if (query.status) {
        queryBuilder.andWhere('metrics.status = :status', { status: query.status });
      }

      // Add ordering
      queryBuilder.orderBy('metrics.reportingPeriod', 'DESC')
        .addOrderBy('orgUnit.name', 'ASC');

      // Apply pagination
      const offset = (query.page - 1) * query.limit;
      queryBuilder.skip(offset).take(query.limit);

      const [metrics, total] = await queryBuilder.getManyAndCount();

      const duration = Date.now() - startTime;
      this.logger.log(`Dashboard query completed in ${duration}ms for user ${requestingUserId}`);

      return { metrics, total };
    } catch (error) {
      const duration = Date.now() - startTime;
      this.logger.error(`Dashboard query failed after ${duration}ms for user ${requestingUserId}`, error);
      throw error;
    }
  }

  /**
   * Create new dashboard metrics
   */
  async createMetrics(
    createDto: CreateManagerDashboardMetricsDto,
    requestingUserId: number,
  ): Promise<ManagerDashboardMetrics> {
    // Validate access permissions
    await this.validateManagerAccess(createDto.managerId, requestingUserId);

    // Check if metrics already exist for this period/unit
    const existingMetrics = await this.metricsRepository.findOne({
      where: {
        organizationalUnitId: createDto.organizationalUnitId,
        reportingPeriod: new Date(createDto.reportingPeriod),
      },
    });

    if (existingMetrics) {
      throw new BadRequestException(
        'Metrics already exist for this organizational unit and reporting period'
      );
    }

    // Validate organizational unit belongs to manager
    await this.validateOrgUnitManagership(createDto.organizationalUnitId, createDto.managerId);

    const metrics = this.metricsRepository.create({
      ...createDto,
      reportingPeriod: new Date(createDto.reportingPeriod),
      lastUpdatedBy: requestingUserId,
    });

    const savedMetrics = await this.metricsRepository.save(metrics);

    // Audit log
    await this.auditLoggingService.logEvent({
      userId: requestingUserId,
      action: 'CREATE_MANAGER_DASHBOARD_METRICS',
      resource: 'manager_dashboard_metrics',
      resourceId: savedMetrics.id.toString(),
      details: {
        organizationalUnitId: createDto.organizationalUnitId,
        managerId: createDto.managerId,
        reportingPeriod: createDto.reportingPeriod,
      },
      severity: 'medium',
      category: 'data_creation',
      outcome: 'success',
      riskLevel: 'medium',
    });

    return this.metricsRepository.findOne({
      where: { id: savedMetrics.id },
      relations: ['organizationalUnit', 'organizationalUnit.parent', 'manager', 'lastUpdatedByUser'],
    });
  }

  /**
   * Update existing dashboard metrics
   */
  async updateMetrics(
    id: number,
    updateDto: UpdateManagerDashboardMetricsDto,
    requestingUserId: number,
  ): Promise<ManagerDashboardMetrics> {
    const existingMetrics = await this.metricsRepository.findOne({
      where: { id },
      relations: ['manager', 'organizationalUnit'],
    });

    if (!existingMetrics) {
      throw new NotFoundException('Dashboard metrics not found');
    }

    await this.validateManagerAccess(existingMetrics.managerId, requestingUserId);

    // Store previous values for audit
    const previousValues = {
      fteCount: existingMetrics.fteCount,
      slaPercentage: existingMetrics.slaPercentage,
      utilizationPercentage: existingMetrics.utilizationPercentage,
      status: existingMetrics.status,
    };

    const updatedMetrics = await this.metricsRepository.save({
      ...existingMetrics,
      ...updateDto,
      lastUpdatedBy: requestingUserId,
      lastUpdatedAt: new Date(),
    });

    // Audit log
    await this.auditLoggingService.logEvent({
      userId: requestingUserId,
      action: 'UPDATE_MANAGER_DASHBOARD_METRICS',
      resource: 'manager_dashboard_metrics',
      resourceId: id.toString(),
      details: {
        organizationalUnit: existingMetrics.organizationalUnit.name,
        changes: updateDto,
        previousValues,
      },
      severity: 'medium',
      category: 'data_modification',
      outcome: 'success',
      riskLevel: 'medium',
    });

    return this.metricsRepository.findOne({
      where: { id: updatedMetrics.id },
      relations: ['organizationalUnit', 'organizationalUnit.parent', 'manager', 'lastUpdatedByUser'],
    });
  }

  /**
   * Get organizational units managed by a user
   */
  async getManagerOrganizationalUnits(
    managerId: number,
    requestingUserId: number,
  ): Promise<OrganizationalUnit[]> {
    await this.validateManagerAccess(managerId, requestingUserId);

    return this.orgUnitRepository.find({
      where: { managerId, isActive: true },
      relations: ['parent', 'manager'],
      order: { name: 'ASC' },
    });
  }

  /**
   * Get all managers for dropdown
   */
  async getManagers(requestingUserId: number): Promise<User[]> {
    const requestingUser = await this.userRepository.findOne({
      where: { id: requestingUserId },
    });

    if (!requestingUser) {
      throw new ForbiddenException('Invalid user');
    }

    // Only allow admin/director/super_admin to see all managers
    if (!['admin', 'director', 'super_admin'].includes(requestingUser.role)) {
      return [requestingUser]; // Return only self
    }

    return this.userRepository.find({
      where: { 
        role: In([UserRole.MANAGER, UserRole.DIRECTOR, UserRole.VP, UserRole.CEO]) 
      },
      order: { firstName: 'ASC', lastName: 'ASC' },
    });
  }

  /**
   * Validate manager access permissions
   */
  private async validateManagerAccess(managerId: number, requestingUserId: number): Promise<void> {
    const requestingUser = await this.userRepository.findOne({
      where: { id: requestingUserId },
    });

    if (!requestingUser) {
      throw new ForbiddenException('Invalid user');
    }

    // Allow access if user is the manager or has admin/director role
    if (
      requestingUserId === managerId ||
      ['admin', 'director', 'super_admin'].includes(requestingUser.role)
    ) {
      return;
    }

    throw new ForbiddenException('Insufficient permissions to access this dashboard');
  }

  /**
   * Get reminder settings for a manager
   */
  async getReminderSettings(
    managerId: number,
    requestingUserId: number,
  ): Promise<DashboardReminderSettings> {
    await this.validateManagerAccess(managerId, requestingUserId);

    let settings = await this.reminderSettingsRepository.findOne({
      where: { managerId },
      relations: ['manager'],
    });

    // Create default settings if none exist
    if (!settings) {
      settings = this.reminderSettingsRepository.create({
        managerId,
        reminderDayOfMonth: 25,
        reminderEnabled: true,
        emailTemplateId: 'monthly_dashboard_reminder',
      });
      settings = await this.reminderSettingsRepository.save(settings);
    }

    return settings;
  }

  /**
   * Update reminder settings for a manager
   */
  async updateReminderSettings(
    managerId: number,
    updateDto: UpdateReminderSettingsDto,
    requestingUserId: number,
  ): Promise<DashboardReminderSettings> {
    await this.validateManagerAccess(managerId, requestingUserId);

    let settings = await this.reminderSettingsRepository.findOne({
      where: { managerId },
    });

    if (!settings) {
      settings = this.reminderSettingsRepository.create({
        managerId,
        ...updateDto,
      });
    } else {
      Object.assign(settings, updateDto);
    }

    const savedSettings = await this.reminderSettingsRepository.save(settings);

    // Audit log
    await this.auditLoggingService.logEvent({
      userId: requestingUserId,
      action: 'UPDATE_DASHBOARD_REMINDER_SETTINGS',
      resource: 'dashboard_reminder_settings',
      resourceId: savedSettings.id.toString(),
      details: {
        managerId,
        changes: updateDto,
      },
      severity: 'low',
      category: 'configuration_change',
      outcome: 'success',
      riskLevel: 'low',
    });

    return this.reminderSettingsRepository.findOne({
      where: { id: savedSettings.id },
      relations: ['manager'],
    });
  }

  /**
   * Calculate FTE count for an organizational unit
   */
  async calculateFteCount(organizationalUnitId: number): Promise<number> {
    const activeUsers = await this.userRepository.count({
      where: {
        organizationalUnitId,
        isActive: true,
      },
    });

    return activeUsers;
  }

  /**
   * Get dashboard summary statistics
   */
  async getDashboardSummary(managerId: number, requestingUserId: number): Promise<any> {
    await this.validateManagerAccess(managerId, requestingUserId);

    const currentMonth = new Date();
    currentMonth.setDate(1); // First day of current month

    const metrics = await this.metricsRepository.find({
      where: {
        managerId,
        reportingPeriod: currentMonth,
      },
      relations: ['organizationalUnit'],
    });

    const totalFte = metrics.reduce((sum, metric) => sum + (metric.fteCount || 0), 0);
    const avgUtilization = metrics.length > 0
      ? metrics.reduce((sum, metric) => sum + (metric.utilizationPercentage || 0), 0) / metrics.length
      : 0;
    const totalAttrition = metrics.reduce((sum, metric) =>
      sum + (metric.attritionResigned || 0) + (metric.attritionInvoluntary || 0), 0);

    return {
      totalFte,
      avgUtilization: Math.round(avgUtilization * 100) / 100,
      totalUnits: metrics.length,
      totalAttrition,
      completedUnits: metrics.filter(m => m.status === DashboardMetricsStatus.APPROVED).length,
      pendingUnits: metrics.filter(m => m.status === DashboardMetricsStatus.DRAFT).length,
    };
  }

  /**
   * Validate that organizational unit belongs to manager
   */
  private async validateOrgUnitManagership(orgUnitId: number, managerId: number): Promise<void> {
    const orgUnit = await this.orgUnitRepository.findOne({
      where: { id: orgUnitId },
    });

    if (!orgUnit) {
      throw new NotFoundException('Organizational unit not found');
    }

    if (orgUnit.managerId !== managerId) {
      throw new ForbiddenException('Manager does not have access to this organizational unit');
    }
  }
}

import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { DashboardReminderSettings } from './entities/dashboard-reminder-settings.entity';
import { ManagerDashboardMetrics } from './entities/manager-dashboard-metrics.entity';
import { User } from '../../users/entities/user.entity';
import { AuditLoggingService } from '../../audit/audit-logging.service';

interface EmailData {
  managerName: string;
  reportingPeriod: string;
  dashboardUrl: string;
  dueDate: string;
  pendingUnits?: string[];
  completedUnits?: number;
  totalUnits?: number;
}

@Injectable()
export class DashboardReminderService {
  private readonly logger = new Logger(DashboardReminderService.name);

  constructor(
    @InjectRepository(DashboardReminderSettings)
    private readonly reminderSettingsRepository: Repository<DashboardReminderSettings>,
    @InjectRepository(ManagerDashboardMetrics)
    private readonly metricsRepository: Repository<ManagerDashboardMetrics>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    private readonly auditLoggingService: AuditLoggingService,
  ) {}

  /**
   * Daily cron job to check and send reminders
   * Runs every day at 9:00 AM
   */
  @Cron(CronExpression.EVERY_DAY_AT_9AM)
  async checkAndSendReminders(): Promise<void> {
    this.logger.log('Starting daily reminder check...');
    
    try {
      const today = new Date();
      const currentDay = today.getDate();

      // Find all managers who should receive reminders today
      const reminderSettings = await this.reminderSettingsRepository.find({
        where: {
          reminderDayOfMonth: currentDay,
          reminderEnabled: true,
        },
        relations: ['manager'],
      });

      this.logger.log(`Found ${reminderSettings.length} managers to send reminders to`);

      for (const setting of reminderSettings) {
        try {
          await this.sendDashboardReminder(setting);
        } catch (error) {
          this.logger.error(
            `Failed to send reminder to manager ${setting.managerId}:`,
            error
          );
        }
      }

      this.logger.log('Daily reminder check completed');
    } catch (error) {
      this.logger.error('Error during daily reminder check:', error);
    }
  }

  /**
   * Send dashboard reminder to a specific manager
   */
  async sendDashboardReminder(setting: DashboardReminderSettings): Promise<void> {
    const manager = setting.manager;
    const reportingPeriod = this.getPreviousMonth();
    
    // Check if manager has already completed dashboard for this period
    const existingMetrics = await this.metricsRepository.find({
      where: {
        managerId: manager.id,
        reportingPeriod,
      },
      relations: ['organizationalUnit'],
    });

    // Get manager's organizational units to check completion status
    const managerUnits = await this.userRepository
      .createQueryBuilder('user')
      .leftJoinAndSelect('user.managedUnits', 'units')
      .where('user.id = :managerId', { managerId: manager.id })
      .getOne();

    const totalUnits = managerUnits?.managedUnits?.length || 0;
    const completedUnits = existingMetrics.filter(m => m.status === 'approved').length;
    const pendingUnits = managerUnits?.managedUnits
      ?.filter(unit => !existingMetrics.some(m => m.organizationalUnitId === unit.id))
      ?.map(unit => unit.name) || [];

    // Only send reminder if there are pending units
    if (pendingUnits.length === 0 && completedUnits === totalUnits) {
      this.logger.log(`Manager ${manager.id} has completed all dashboard submissions, skipping reminder`);
      return;
    }

    const emailData: EmailData = {
      managerName: `${manager.firstName} ${manager.lastName}`,
      reportingPeriod: reportingPeriod.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
      }),
      dashboardUrl: `${process.env.FRONTEND_URL || 'http://localhost:3080'}/analytics/manager-dashboard`,
      dueDate: this.getDueDate().toLocaleDateString(),
      pendingUnits,
      completedUnits,
      totalUnits,
    };

    // In a real implementation, you would integrate with an email service
    // For now, we'll log the email content and create an audit entry
    await this.logEmailReminder(manager, emailData, setting.emailTemplateId);

    // Audit log the reminder
    await this.auditLoggingService.logEvent({
      userId: null, // System action
      action: 'SEND_DASHBOARD_REMINDER',
      resource: 'dashboard_reminder',
      resourceId: setting.id.toString(),
      details: {
        managerId: manager.id,
        managerEmail: manager.email,
        reportingPeriod: emailData.reportingPeriod,
        pendingUnits: pendingUnits.length,
        totalUnits,
      },
      severity: 'low',
      category: 'notification',
      outcome: 'success',
      riskLevel: 'low',
    });

    this.logger.log(`Dashboard reminder sent to ${manager.email} for period ${emailData.reportingPeriod}`);
  }

  /**
   * Send manual reminder (for testing or manual triggers)
   */
  async sendManualReminder(managerId: number): Promise<void> {
    const setting = await this.reminderSettingsRepository.findOne({
      where: { managerId },
      relations: ['manager'],
    });

    if (!setting) {
      throw new Error(`No reminder settings found for manager ${managerId}`);
    }

    await this.sendDashboardReminder(setting);
  }

  /**
   * Get previous month for reporting period
   */
  private getPreviousMonth(): Date {
    const date = new Date();
    date.setMonth(date.getMonth() - 1);
    date.setDate(1); // First day of the month
    return date;
  }

  /**
   * Get due date (7 days from now)
   */
  private getDueDate(): Date {
    const date = new Date();
    date.setDate(date.getDate() + 7);
    return date;
  }

  /**
   * Log email reminder (placeholder for actual email service integration)
   */
  private async logEmailReminder(
    manager: User,
    emailData: EmailData,
    templateId: string,
  ): Promise<void> {
    // This is where you would integrate with your email service
    // For example: SendGrid, AWS SES, Nodemailer, etc.
    
    const emailContent = this.generateEmailContent(emailData, templateId);
    
    this.logger.log(`EMAIL REMINDER - To: ${manager.email}`);
    this.logger.log(`Subject: Monthly Dashboard Update Required - ${emailData.reportingPeriod}`);
    this.logger.log(`Content: ${emailContent}`);
    
    // TODO: Replace with actual email service integration
    // await this.emailService.sendTemplatedEmail({
    //   to: manager.email,
    //   templateId,
    //   data: emailData,
    //   subject: `Monthly Dashboard Update Required - ${emailData.reportingPeriod}`,
    // });
  }

  /**
   * Generate email content based on template
   */
  private generateEmailContent(emailData: EmailData, templateId: string): string {
    switch (templateId) {
      case 'monthly_dashboard_reminder':
      default:
        return `
Dear ${emailData.managerName},

This is a reminder to update your monthly dashboard for ${emailData.reportingPeriod}.

Dashboard Status:
- Total Units: ${emailData.totalUnits}
- Completed: ${emailData.completedUnits}
- Pending: ${emailData.pendingUnits?.length || 0}

${emailData.pendingUnits?.length ? `
Pending Units:
${emailData.pendingUnits.map(unit => `- ${unit}`).join('\n')}
` : ''}

Please complete your dashboard submission by ${emailData.dueDate}.

Access your dashboard: ${emailData.dashboardUrl}

Best regards,
AEVEN HR System
        `.trim();
    }
  }

  /**
   * Get reminder statistics for monitoring
   */
  async getReminderStatistics(): Promise<any> {
    const totalSettings = await this.reminderSettingsRepository.count();
    const enabledSettings = await this.reminderSettingsRepository.count({
      where: { reminderEnabled: true },
    });

    const today = new Date();
    const currentDay = today.getDate();
    const todayReminders = await this.reminderSettingsRepository.count({
      where: {
        reminderDayOfMonth: currentDay,
        reminderEnabled: true,
      },
    });

    return {
      totalManagers: totalSettings,
      enabledReminders: enabledSettings,
      todayReminders,
      reminderDay: currentDay,
    };
  }
}
